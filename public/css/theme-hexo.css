/*  菜单下划线动画 */
#theme-hexo .menu-link {
    text-decoration: none;
    background-image: linear-gradient(#928CEE, #928CEE);
    background-repeat: no-repeat;
    background-position: bottom center;
    background-size: 0 2px;
    transition: background-size 100ms ease-in-out;
}
 
#theme-hexo .menu-link:hover {
    background-size: 100% 2px;
    color: #928CEE;
}

/* 设置了从上到下的渐变黑色 */
#theme-hexo .header-cover::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:  linear-gradient(to bottom, rgba(0,0,0,0.5) 0%, rgba(0,0,0,0.2) 10%, rgba(0,0,0,0) 25%, rgba(0,0,0,0.2) 75%, rgba(0,0,0,0.5) 100%);
}

/* Custem */
.tk-footer{
    opacity: 0;
}