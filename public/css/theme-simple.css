#theme-simple #announcement-content {
  /* background-color: #f6f6f6; */
}

#theme-simple #blog-item-title {
  color: #276077;
}

.dark #theme-simple #blog-item-title {
  color: #d1d5db;
}

.notion {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}


/*  菜单下划线动画 */
#theme-simple .menu-link {
    text-decoration: none;
    background-image: linear-gradient(#dd3333, #dd3333);
    background-repeat: no-repeat;
    background-position: bottom center;
    background-size: 0 2px;
    transition: background-size 100ms ease-in-out;
}
 
#theme-simple .menu-link:hover {
    background-size: 100% 2px;
    color: #dd3333;
}

