export default {
  LOCALE: 'en-US',
  NAV: {
    INDEX: 'Blog',
    RSS: 'RSS',
    SEARCH: 'Search',
    ABOUT: 'About',
    MAIL: 'E-Mail',
    ARCHIVE: 'Archive'
  },
  COMMON: {
    MORE: 'More',
    NO_MORE: 'No More',
    LATEST_POSTS: 'Latest posts',
    TAGS: 'Tags',
    NO_TAG: 'NoTag',
    CATEGORY: 'Category',
    SHARE: 'Share',
    SCAN_QR_CODE: 'Scan QRCode',
    URL_COPIED: 'U<PERSON> has copied!',
    TABLE_OF_CONTENTS: 'Catalog',
    RELATE_POSTS: 'Relate Posts',
    COPYRIGHT: 'Copyright',
    AUTHOR: 'Author',
    URL: 'URL',
    POSTS: 'Posts',
    ARTICLE: 'Article',
    VISITORS: 'Visitors',
    VIEWS: 'Views',
    COPYRIGHT_NOTICE: 'All articles in this blog, except for special statements, adopt BY-NC-SA agreement. Please indicate the source!',
    RESULT_OF_SEARCH: 'Results Found',
    ARTICLE_DETAIL: 'Article Details',
    PASSWORD_ERROR: 'Password Error!',
    ARTICLE_LOCK_TIPS: 'Please Enter the password:',
    SUBMIT: 'Submit',
    POST_TIME: 'Post on',
    LAST_EDITED_TIME: 'Last edited',
    RECENT_COMMENTS: 'Recent Comments',
    DEBUG_OPEN: 'Debug',
    DEBUG_CLOSE: 'Close',
    THEME_SWITCH: 'Theme Switch',
    ANNOUNCEMENT: 'Announcement',
    START_READING: 'Start Reading',
    MINUTE: 'min',
    WORD_COUNT: 'W.C.'

  },
  PAGINATION: {
    PREV: 'Prev',
    NEXT: 'Next'
  },
  SEARCH: {
    ARTICLES: 'Search Articles',
    TAGS: 'Search in'
  },
  POST: {
    BACK: 'Back',
    TOP: 'Top'
  }
}
