export const SVGCircleBG = () => {
  return <svg
    width="48"
    height="134"
    viewBox="0 0 48 134"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle
      cx="45.6673"
      cy="132"
      r="1.66667"
      transform="rotate(180 45.6673 132)"
      fill="#13C296"
    />
    <circle
      cx="45.6673"
      cy="117.333"
      r="1.66667"
      transform="rotate(180 45.6673 117.333)"
      fill="#13C296"
    />
    <circle
      cx="45.6673"
      cy="102.667"
      r="1.66667"
      transform="rotate(180 45.6673 102.667)"
      fill="#13C296"
    />
    <circle
      cx="45.6673"
      cy="88.0001"
      r="1.66667"
      transform="rotate(180 45.6673 88.0001)"
      fill="#13C296"
    />
    <circle
      cx="45.6673"
      cy="73.3335"
      r="1.66667"
      transform="rotate(180 45.6673 73.3335)"
      fill="#13C296"
    />
    <circle
      cx="45.6673"
      cy="45.0001"
      r="1.66667"
      transform="rotate(180 45.6673 45.0001)"
      fill="#13C296"
    />
    <circle
      cx="45.6673"
      cy="16.0001"
      r="1.66667"
      transform="rotate(180 45.6673 16.0001)"
      fill="#13C296"
    />
    <circle
      cx="45.6673"
      cy="59.0001"
      r="1.66667"
      transform="rotate(180 45.6673 59.0001)"
      fill="#13C296"
    />
    <circle
      cx="45.6673"
      cy="30.6668"
      r="1.66667"
      transform="rotate(180 45.6673 30.6668)"
      fill="#13C296"
    />
    <circle
      cx="45.6673"
      cy="1.66683"
      r="1.66667"
      transform="rotate(180 45.6673 1.66683)"
      fill="#13C296"
    />
    <circle
      cx="31.0013"
      cy="132"
      r="1.66667"
      transform="rotate(180 31.0013 132)"
      fill="#13C296"
    />
    <circle
      cx="31.0013"
      cy="117.333"
      r="1.66667"
      transform="rotate(180 31.0013 117.333)"
      fill="#13C296"
    />
    <circle
      cx="31.0013"
      cy="102.667"
      r="1.66667"
      transform="rotate(180 31.0013 102.667)"
      fill="#13C296"
    />
    <circle
      cx="31.0013"
      cy="88.0001"
      r="1.66667"
      transform="rotate(180 31.0013 88.0001)"
      fill="#13C296"
    />
    <circle
      cx="31.0013"
      cy="73.3335"
      r="1.66667"
      transform="rotate(180 31.0013 73.3335)"
      fill="#13C296"
    />
    <circle
      cx="31.0013"
      cy="45.0001"
      r="1.66667"
      transform="rotate(180 31.0013 45.0001)"
      fill="#13C296"
    />
    <circle
      cx="31.0013"
      cy="16.0001"
      r="1.66667"
      transform="rotate(180 31.0013 16.0001)"
      fill="#13C296"
    />
    <circle
      cx="31.0013"
      cy="59.0001"
      r="1.66667"
      transform="rotate(180 31.0013 59.0001)"
      fill="#13C296"
    />
    <circle
      cx="31.0013"
      cy="30.6668"
      r="1.66667"
      transform="rotate(180 31.0013 30.6668)"
      fill="#13C296"
    />
    <circle
      cx="31.0013"
      cy="1.66683"
      r="1.66667"
      transform="rotate(180 31.0013 1.66683)"
      fill="#13C296"
    />
    <circle
      cx="16.3333"
      cy="132"
      r="1.66667"
      transform="rotate(180 16.3333 132)"
      fill="#13C296"
    />
    <circle
      cx="16.3333"
      cy="117.333"
      r="1.66667"
      transform="rotate(180 16.3333 117.333)"
      fill="#13C296"
    />
    <circle
      cx="16.3333"
      cy="102.667"
      r="1.66667"
      transform="rotate(180 16.3333 102.667)"
      fill="#13C296"
    />
    <circle
      cx="16.3333"
      cy="88.0001"
      r="1.66667"
      transform="rotate(180 16.3333 88.0001)"
      fill="#13C296"
    />
    <circle
      cx="16.3333"
      cy="73.3335"
      r="1.66667"
      transform="rotate(180 16.3333 73.3335)"
      fill="#13C296"
    />
    <circle
      cx="16.3333"
      cy="45.0001"
      r="1.66667"
      transform="rotate(180 16.3333 45.0001)"
      fill="#13C296"
    />
    <circle
      cx="16.3333"
      cy="16.0001"
      r="1.66667"
      transform="rotate(180 16.3333 16.0001)"
      fill="#13C296"
    />
    <circle
      cx="16.3333"
      cy="59.0001"
      r="1.66667"
      transform="rotate(180 16.3333 59.0001)"
      fill="#13C296"
    />
    <circle
      cx="16.3333"
      cy="30.6668"
      r="1.66667"
      transform="rotate(180 16.3333 30.6668)"
      fill="#13C296"
    />
    <circle
      cx="16.3333"
      cy="1.66683"
      r="1.66667"
      transform="rotate(180 16.3333 1.66683)"
      fill="#13C296"
    />
    <circle
      cx="1.66732"
      cy="132"
      r="1.66667"
      transform="rotate(180 1.66732 132)"
      fill="#13C296"
    />
    <circle
      cx="1.66732"
      cy="117.333"
      r="1.66667"
      transform="rotate(180 1.66732 117.333)"
      fill="#13C296"
    />
    <circle
      cx="1.66732"
      cy="102.667"
      r="1.66667"
      transform="rotate(180 1.66732 102.667)"
      fill="#13C296"
    />
    <circle
      cx="1.66732"
      cy="88.0001"
      r="1.66667"
      transform="rotate(180 1.66732 88.0001)"
      fill="#13C296"
    />
    <circle
      cx="1.66732"
      cy="73.3335"
      r="1.66667"
      transform="rotate(180 1.66732 73.3335)"
      fill="#13C296"
    />
    <circle
      cx="1.66732"
      cy="45.0001"
      r="1.66667"
      transform="rotate(180 1.66732 45.0001)"
      fill="#13C296"
    />
    <circle
      cx="1.66732"
      cy="16.0001"
      r="1.66667"
      transform="rotate(180 1.66732 16.0001)"
      fill="#13C296"
    />
    <circle
      cx="1.66732"
      cy="59.0001"
      r="1.66667"
      transform="rotate(180 1.66732 59.0001)"
      fill="#13C296"
    />
    <circle
      cx="1.66732"
      cy="30.6668"
      r="1.66667"
      transform="rotate(180 1.66732 30.6668)"
      fill="#13C296"
    />
    <circle
      cx="1.66732"
      cy="1.66683"
      r="1.66667"
      transform="rotate(180 1.66732 1.66683)"
      fill="#13C296"
    />
  </svg>
}
