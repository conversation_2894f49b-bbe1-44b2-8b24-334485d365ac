{"name": "notion-next", "version": "3.14.0", "homepage": "https://github.com/tangly1024/NotionNext.git", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tangly1024/NotionNext.git"}, "author": {"name": "tangly", "email": "<EMAIL>", "url": "http://tangly1024.com"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "post-build": "next-sitemap --config next-sitemap.config.js", "bundle-report": "ANALYZE=true yarn build"}, "dependencies": {"@giscus/react": "^2.2.6", "@next/bundle-analyzer": "^12.1.1", "@vercel/analytics": "^1.0.0", "animate.css": "^4.1.1", "animejs": "^3.2.1", "aos": "^3.0.0-beta.6", "axios": ">=0.21.1", "copy-to-clipboard": "^3.3.1", "eslint-plugin-react-hooks": "^4.6.0", "feed": "^4.2.2", "gitalk": "^1.7.2", "js-md5": "^0.7.3", "localStorage": "^1.0.4", "lodash.throttle": "^4.1.1", "mark.js": "^8.11.1", "memory-cache": "^0.2.0", "mermaid": "9.2.2", "mongodb": "^4.6.0", "next": "13.3.1", "notion-client": "6.15.6", "notion-utils": "6.15.6", "nprogress": "^0.2.0", "preact": "^10.5.15", "prism-themes": "1.9.0", "qrcode.react": "^1.0.1", "react": "^18.2.0", "react-cookies": "^0.1.1", "react-cusdis": "^2.1.3", "react-dom": "^18.2.0", "react-facebook": "^8.1.4", "react-messenger-customer-chat": "^0.8.0", "react-notion-x": "6.16.0", "react-share": "^4.4.1", "react-tweet-embed": "~2.0.0", "smoothscroll-polyfill": "^0.4.4", "typed.js": "^2.0.12", "use-ackee": "^3.0.0", "valine": "^1.4.18"}, "devDependencies": {"@waline/client": "^2.5.1", "autoprefixer": "^10.4.13", "eslint": "^7.26.0", "eslint-config-next": "^13.1.1", "eslint-config-standard": "^16.0.2", "eslint-plugin-import": "^2.23.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-react": "^7.23.2", "next-sitemap": "^1.6.203", "postcss": "^8.4.20", "tailwindcss": "^3.2.4", "webpack-bundle-analyzer": "^4.5.0"}, "resolutions": {"axios": ">=0.21.1"}, "bugs": {"url": "https://github.com/tangly/NotionNext/issues", "email": "<EMAIL>"}}