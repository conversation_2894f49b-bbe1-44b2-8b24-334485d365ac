@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  --scrollbarBG: #ffffff00;
  --thumbBG: #b8b8b8;
}
body::-webkit-scrollbar {
  width: 5px;
}
body {
  scrollbar-width: thin;
  scrollbar-color: var(--thumbBG) var(--scrollbarBG);
}
body::-webkit-scrollbar-track {
  background: var(--scrollbarBG);
}
body::-webkit-scrollbar-thumb {
  background-color: var(--thumbBG);
}

::selection {
  background: rgba(45, 170, 219, 0.3);
}

.wrapper {
  min-height: 100vh;
  display: flex;
  flex-wrap: nowrap;
  align-items: stretch;
  justify-content: flex-start;
  flex-direction: column;
}

.sticky-nav {
  position: sticky;
  z-index: 10;
  top: -1px;
  backdrop-filter: blur(5px);
  transition: all 0.5s cubic-bezier(0.4, 0, 0, 1);
  border-bottom-color: transparent;
}

.sticky-nav-full {
  @apply border-b border-opacity-50 border-gray-200 dark:border-gray-600 dark:border-opacity-50;
}

.header-name {
  overflow: hidden;
}

.sticky-nav-full .nav {
  @apply text-gray-600 dark:text-gray-300;
}

nav {
  flex-wrap: wrap;
  line-height: 1.5em;
}

.article-tags::-webkit-scrollbar {
  width: 0 !important;
}

.tag-container ul::-webkit-scrollbar {
  width: 0 !important;
}

.tag-container ul {
  -ms-overflow-style: none;
  overflow: -moz-scrollbars-none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  user-select: none;
}

@media (min-width: 768px) {
  .sticky-nav-full {
    @apply max-w-full border-b border-opacity-50 border-gray-200 dark:border-gray-600 dark:border-opacity-50;
  }
  .header-name {
    display: block;
    transition: all 0.5s cubic-bezier(0.4, 0, 0, 1);
  }
  .sticky-nav-full .header-name {
    @apply dark:text-gray-300 text-gray-600;
  }
}

@supports not (backdrop-filter: none) {
  .sticky-nav {
    backdrop-filter: none;
    @apply bg-day dark:bg-gray-800;
  }
}

.shadow-card {
  box-shadow: rgba(0, 0, 0, 0.07) 0px 1px 2px, rgba(0, 0, 0, 0.07) 0px 2px 4px,
    rgba(0, 0, 0, 0.07) 0px 4px 8px, rgba(0, 0, 0, 0.07) 0px 8px 16px,
    rgba(0, 0, 0, 0.07) 0px 16px 32px, rgba(0, 0, 0, 0.07) 0px 32px 64px;
}

.gt-meta {
  @apply dark:text-gray-300;
}

#waifu {
  @apply right-auto left-0 hidden lg:block z-10 !important;
}

/* 隐藏滚动条 */
.scroll-hidden {
  -ms-overflow-style: none;
  overflow: -moz-scrollbars-none;
  scrollbar-width: none; /* firefox */
}

.scroll-hidden::-webkit-scrollbar {
  width: 0 !important;
}

.notion-collection {
  @apply max-w-0;
}

.glassmorphism {
  background: hsla(0, 0%, 100%, 0.05);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}


.medium-zoom-overlay {
  background: none !important;
  /* background: rgba(0, 0, 0, 0.01) none repeat scroll 0% 0% !important; */
}

.shadow-text {
  text-shadow: 0.1em 0.1em 0.2em black;
}

.notion-code-copy-button > svg {
  pointer-events: none;
}

.fireworks {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  pointer-events: none;
}

[data-waline] p {
  color: var(--waline-color);
  @apply dark:text-gray-200 !important;
}

.waline-recent-content p {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.waline-recent-content .wl-emoji {
  height: 1.1rem !important;
  display: inline-block !important;
  line-height: 1.25rem !important;
  vertical-align: text-bottom !important;
}

.vcontent .wl-emoji {
  display: inline-block;
  vertical-align: baseline;
  height: 1.25em;
  margin: -0.125em 0.25em;
}

/* 固定两行 */
.text-line-2 {
    overflow : hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	word-wrap: break-word;
	word-break: break-all;
}

.nobelium{
    @apply flex flex-col justify-between
}

.nobelium .notion-code{
    /* @apply max-w-2xl; */
}

.next #announcement-content *{
 font-size:13px !important;
 line-height:1.7 !important;
}

/* twikoo 评论区超链接样式 */
.tk-main a {
   @apply text-blue-700
}

/* Webmention style */
.webmention-block {
  background: rgba(0, 116, 222, .2);
  padding: 1rem 2rem;
  border-radius: 5px;
}

.webmention-header {
  font-style: italic;
  font-weight: 700;
  font-size: 16px;
  margin-bottom: .5rem;
}

.webmention-block-intro a {
  color: #0000EE;
  text-decoration: underline;
}

.webmention {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.2);
}

.webmention-counts {
  padding: 16px 0;
  font-weight: bold;
}

.webmention-counts .count {
  font-weight: bold;
  margin-right: .2rem;
}

/* .webmention-counts .counts > span {
  margin-right: .8rem;
} */
.webmention-counts .counts>span:not(:last-child):after {
  content: " • ";
}

a.avatar-wrapper {
  display: inline-block;
  width: 50px;
  height: 50px;
  position: relative;
}

.webmention-avatars .avatar-wrapper {
  margin-right: -8px;
}

.avatar {
  border-radius: 50%;
  margin: 0;
  border: 3px solid rgba(0, 116, 222, .5);
}

.replies {
  margin: 0;
  padding: 0;
}

.reply {
  list-style: none;
  display: flex;
  position: relative;
  padding: 0;
  align-items: flex-start;
  margin-top: .6rem;
}

.reply p {
  margin: 0;
}

.reply .text {
  margin-left: 1rem;
  font-size: 14px;
}

.reply-author-name {
  font-weight: 500;
}

.p-4-lines {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  overflow: hidden;
  text-overflow: ellipsis;
}

.p-3-lines {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* fukasawa的首页响应式分栏 */
#theme-fukasawa .grid-item {
    height: auto;
    break-inside: avoid-column;
    margin-bottom: .5rem;
  }
  
  /* 大屏幕（宽度≥1024px）下显示3列 */
  @media (min-width: 1024px) {
    #theme-fukasawa .grid-container {
      column-count: 3;
      column-gap: .5rem;
    }
  }
  
  /* 小屏幕（宽度≥640px）下显示2列 */
  @media (min-width: 640px) and (max-width: 1023px) {
    #theme-fukasawa .grid-container {
      column-count: 2;
      column-gap: .5rem;
    }
  }
  
  /* 移动端（宽度<640px）下显示1列 */
  @media (max-width: 639px) {
    #theme-fukasawa .grid-container {
      column-count: 1;
      column-gap: .5rem;
    }
  }